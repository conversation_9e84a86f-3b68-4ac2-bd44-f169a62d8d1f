# stocks

## 项目简介

本项目用于收集和管理上市公司年度及季度报告（PDF 格式），便于后续查阅、分析和整理。

## 目录结构

```
stocks/
  ├── 2024/    # 存放 2024 年的年度报告
  ├── 2025/    # 存放 2025 年的季度报告
  └── README.md
```

- `2024/` 目录下为 2024 年各上市公司的年度报告 PDF 文件。
- `2025/` 目录下为 2025 年各上市公司的季度报告 PDF 文件。

## 文件命名规范

报告文件命名格式为：

```
公司名_年份_报告类型.pdf
```

例如：
- 招商银行_2024年年度报告.pdf
- 格力电器_2025年第一季度报告.pdf

## 如何扩展

1. 按照现有目录结构，将新的报告 PDF 文件放入对应年份文件夹。
2. 命名时请遵循上述规范，便于检索和管理。

## 未来计划

- 增加自动化脚本，实现报告的批量下载、整理和检索。
- 支持报告内容的自动解析与数据提取。

## 贡献

欢迎提交 PR 或 issue 以完善本项目。

## 许可证

本项目仅用于学习和个人研究用途，报告版权归原公司所有。